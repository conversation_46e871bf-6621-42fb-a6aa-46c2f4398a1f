"""Module for writing data to Google Sheets output worksheet.

Provides functions to clear and update the output sheet with parsed product data.
"""

from src.google_client import get_output_sheet


def clear_output_sheet():
    """
    Löscht alle Daten unterhalb der Kopfzeile (ab Zeile 2).
    """
    sheet = get_output_sheet()
    num_rows = len(sheet.get_all_values())
    if num_rows > 1:
        sheet.batch_clear([f"A2:F{num_rows}"])


def write_output_data(parsed_data_list):
    """
    Schreibt die geparsten Produktdaten in das Output-Sheet.

    Die Daten werden als neue Zeilen an das Sheet angehängt. Jeder Eintrag in der Liste
    wird als eine Zeile mit den Spalten ID, Price1, Delivery1, Price2, Delivery2 und
    Vendor Name geschrieben.

    Args:
        parsed_data_list (List[Dict]): Liste von Dictionaries mit den Produktdaten
    """
    sheet = get_output_sheet()
    rows_to_append = []

    for entry in parsed_data_list:
        row = [
            entry.get("ID", ""),
            entry.get("Price1", ""),
            entry.get("Delivery1", ""),
            entry.get("Price2", ""),
            entry.get("Delivery2", ""),
            entry.get("Vendor Name", ""),
        ]
        rows_to_append.append(row)

    if rows_to_append:
        sheet.append_rows(rows_to_append, value_input_option="USER_ENTERED")
