"""Module for handling Google Sheets API connections and operations.

Provides functions to authenticate with Google Sheets API and access input/output spreadsheets.
"""

import os
import gspread
from oauth2client.service_account import ServiceAccountCredentials
from config.settings import GOOGLE_SHEET_IN, GOOGLE_SHEET_OUT

# Dynamisch den absoluten Pfad zur Credential-Datei ermitteln
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
CRED_PATH = os.path.join(BASE_DIR, "credentials", "altruan-de-f467982cc39f.json")


def get_gspread_client():
    """Create and return an authenticated Google Sheets client.

    Authenticates using service account credentials from the credentials file.

    Returns:
        gspread.Client: Authenticated gspread client ready for API operations.
    """
    scope = [
        "https://spreadsheets.google.com/feeds",
        "https://www.googleapis.com/auth/drive",
    ]
    creds = ServiceAccountCredentials.from_json_keyfile_name(CRED_PATH, scope)
    return gspread.authorize(creds)


def get_input_sheet():
    """Get the input worksheet from Google Sheets.

    Uses the spreadsheet ID and sheet name from GOOGLE_SHEET_IN configuration.

    Returns:
        gspread.Worksheet: The input worksheet object.
    """
    client = get_gspread_client()
    return client.open_by_key(GOOGLE_SHEET_IN["spreadsheet_id"]).worksheet(
        GOOGLE_SHEET_IN["sheet_name"]
    )


def get_output_sheet():
    """Get the output worksheet from Google Sheets.

    Uses the spreadsheet ID and sheet name from GOOGLE_SHEET_OUT configuration.

    Returns:
        gspread.Worksheet: The output worksheet object.
    """
    client = get_gspread_client()
    return client.open_by_key(GOOGLE_SHEET_OUT["spreadsheet_id"]).worksheet(
        GOOGLE_SHEET_OUT["sheet_name"]
    )


def get_spreadsheet():
    """Get the entire output spreadsheet from Google Sheets.

    Uses the spreadsheet ID from GOOGLE_SHEET_OUT configuration.

    Returns:
        gspread.Spreadsheet: The output spreadsheet object.
    """
    client = get_gspread_client()
    return client.open_by_key(GOOGLE_SHEET_OUT["spreadsheet_id"])
