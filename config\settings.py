import os

# BASE URL für Produktseiten
BASE_URL = "https://wawibox.de/preisvergleich/"

# Input Google Sheet – Quelle der URL-Extensions/Metro-IDs
GOOGLE_SHEET_IN = {
    "spreadsheet_id": "1VLjP0IdlvooSLYcMRbmGFqvThUIurWCh-Ej3YxpzgIo",
    "sheet_name": "Input",
}

# Output Google Sheet – Ziel für Ergebnisse
GOOGLE_SHEET_OUT = {
    "spreadsheet_id": "15njznUBOn6M6_y01osalDgHS5pZhniJj_KZ-2dRLp9M",
    "sheet_name": "Output",
}

# Timeout für Requests
REQUEST_TIMEOUT = 10

# Logging

# Absoluter Pfad zum Projektverzeichnis
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
LOG_FILE = os.path.join(BASE_DIR, "logs", "scraper.log")

LOG_LEVEL = "INFO"
