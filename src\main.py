"""Main module for the Metro web scraper application.

This module orchestrates the entire scraping process:
1. Fetches product IDs from the input sheet
2. Retrieves HTML content for each product
3. Parses the product data
4. Writes results to the output sheet
5. Copies final data to the main sheet
"""

import logging
from math import ceil
import os
import io

from tqdm import tqdm

from config.settings import LOG_FILE, LOG_LEVEL
from src.fetcher import fetch_html
from src.parser import parse_product_page
from src.sheets_reader import get_url_batch, get_total_ids_count
from src.sheets_utils import replace_sheet_content
from src.sheets_writer import write_output_data, clear_output_sheet

class TqdmLoggingHandler(logging.Handler):
    """Logging handler that writes to tqdm without breaking the progress bar."""
    def __init__(self, level=logging.NOTSET):
        super().__init__(level)
        self.stream = io.StringIO()

    def emit(self, record):
        try:
            msg = self.format(record)
            # Skip error messages about encoding
            if "UnicodeEncodeError" in msg or "Traceback" in msg:
                return
            tqdm.write(msg)
            self.flush()
        except Exception:  # pylint: disable=broad-exception-caught
            pass  # Silently ignore any errors in the handler itself

def setup_logging():
    """Configure logging for the application."""
    # Create logs directory if it doesn't exist
    os.makedirs(os.path.dirname(LOG_FILE), exist_ok=True)

    # Root logger configuration
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, LOG_LEVEL))

    # Remove all existing handlers to avoid duplicate logs
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # File handler - detailed logs to file
    file_handler = logging.FileHandler(LOG_FILE, encoding='utf-8')
    file_handler.setLevel(getattr(logging, LOG_LEVEL))
    file_handler.setFormatter(logging.Formatter(
        "%(asctime)s [%(levelname)s] %(message)s"
    ))

    # Console handler - minimal logs to console that work with tqdm
    console_handler = TqdmLoggingHandler()
    console_handler.setLevel(logging.INFO)  # Only INFO and above for console
    console_handler.setFormatter(logging.Formatter(
        "%(message)s"  # Simplified format for console
    ))

    # Add handlers to root logger
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)

def main(batch_size=100):
    """Execute the main scraping workflow.

    The function processes product IDs in batches, fetches HTML content for each product,
    parses the data, and writes results to the output sheet. Finally, it copies the
    results to the main sheet.

    Args:
        batch_size (int, optional): Number of products to process in each batch. Defaults to 100.
    """
    # Initialize logging system
    setup_logging()

    # Get total number of products to process
    total_ids = get_total_ids_count()
    total_batches = ceil(total_ids / batch_size)

    print(f"Processing {total_ids} IDs in {total_batches} batches of {batch_size}")
    logging.info("Processing %d IDs in %d batches of %d", total_ids, total_batches, batch_size)

    # Prepare output sheet
    clear_output_sheet()  # Clear only once at the beginning

    # Process each batch of products
    for batch_index in range(total_batches):
        # Get URLs for this batch
        url_extensions = get_url_batch(batch_size=batch_size, batch_index=batch_index)
        results = []

        # Create progress bar for this batch
        progress_bar = tqdm(
            url_extensions,
            desc=f"Batch {batch_index + 1}/{total_batches}",
            ncols=100,  # Fixed width
            bar_format="{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}]"
        )

        # Process each product in the batch
        for mp_id in progress_bar:
            # Fetch HTML content
            html, status = fetch_html(mp_id)
            if html and status == 200:
                try:
                    # Parse product data
                    parsed = parse_product_page(html, mp_id)
                    if parsed["Price1"] or parsed["Vendor Name"]:
                        # Add successful result to batch
                        results.append(parsed)
                        # Update progress bar description with success
                        progress_bar.set_postfix_str(f"Success: {mp_id[:15]}...")
                    else:
                        # Update progress bar description with no data
                        progress_bar.set_postfix_str(f"No data: {mp_id[:15]}...")
                except Exception as e:  # pylint: disable=broad-exception-caught
                    # Log full error to file, brief to console
                    logging.error("Parsing error for %s: %s", mp_id, e)
                    progress_bar.set_postfix_str(f"Error: {mp_id[:15]}...")
            else:
                # Log HTTP errors
                logging.warning("HTTP %d for %s", status, mp_id)
                progress_bar.set_postfix_str(f"HTTP {status}: {mp_id[:15]}...")

        # Write batch results to output sheet
        if results:
            write_output_data(results)
            print(f"Batch {batch_index + 1}/{total_batches}: {len(results)} results")
            logging.info("Batch %d/%d: %d results", batch_index + 1, total_batches, len(results))
        else:
            print(f"Batch {batch_index + 1}/{total_batches}: no results")
            logging.warning("Batch %d/%d: no results", batch_index + 1, total_batches)

    # Copy data to Main sheet
    replace_sheet_content(source_sheet_name="Output", target_sheet_name="Main")
    print("Data copied to 'Main' sheet")
    logging.info("Data copied to 'Main' sheet")

if __name__ == "__main__":
    main()
