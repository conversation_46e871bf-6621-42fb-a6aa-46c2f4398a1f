"""Fetcher module for retrieving HTML content from Metro website.

This module handles HTTP requests to fetch product pages.
"""

import logging
import time
import random
from typing import Tuple, Optional

import requests
from requests.exceptions import RequestException

from config.settings import BASE_URL, REQUEST_TIMEOUT


def fetch_html(product_id: str) -> Tuple[Optional[str], int]:
    """Fetch HTML content for a given product ID.

    Args:
        product_id: The product ID to fetch

    Returns:
        A tuple containing (HTML content or None, HTTP status code)
    """
    url = f"{BASE_URL}{product_id}"
    headers = {
        "User-Agent": (
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
            "(KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        )
    }

    try:
        # Add a small random delay to avoid rate limiting
        time.sleep(random.uniform(0.5, 1.5))

        # Make the HTTP request
        response = requests.get(url, headers=headers, timeout=REQUEST_TIMEOUT)

        # Check if the request was successful
        if response.status_code == 200:
            return response.text, response.status_code

        # Log non-200 responses
        logging.warning("HTTP %d for URL: %s", response.status_code, url)
        return None, response.status_code

    except RequestException as e:
        # Log request exceptions
        logging.error("Request error for %s: %s", url, e)
        return None, 0
    except Exception as e:  # pylint: disable=broad-exception-caught
        # Log any other exceptions
        logging.error("Unexpected error for %s: %s", url, e)
        return None, 0


def fetch_offer_data(product_uuid):
    """Fetch offer data for a product from the API."""
    api_url = (
        f"https://api.wawibox.de/api/v1/public/product-prices/{product_uuid}"
    )

    try:
        response = requests.get(api_url, timeout=REQUEST_TIMEOUT)
        response.raise_for_status()  # Raise an exception for 4XX/5XX responses
        return response.json()
    except requests.exceptions.RequestException as e:
        logging.error("❌ API-Fehler für UUID %s: %s", product_uuid, e)
        return None
