product_scraper/
│
├── config/
│   └── settings.py           # Zentrale Konfiguration (BaseURL, Sheet-ID, etc.)
│
├── credentials/
│   └── service_account.json  # Google API Credentials
│
├── data/
│   └── url_extensions.txt    # Liste der URL-Extensions (CRLF, Windows)
│
├── src/
│   ├── __init__.py
│   ├── fetcher.py            # Ruft HTML von Produktseiten ab
│   ├── parser.py             # Parst HTML und extrahiert Daten (z. B. Preis)
│   ├── sheets_writer.py      # Google Sheets Interaktion
│   └── main.py               # Hauptskript, orchestriert alles
│
├── logs/
│   └── scraper.log           # Laufzeit-Logs
│
├── requirements.txt          # Python-Abhängigkeiten
├── .env                      # Umgebungsvariablen (falls nötig)
└── README.md                 # Projektbeschreibung

_____________________________________________________________________________________

┌────────────────────┐
│ url_extensions.txt │
└────────┬───────────┘
         │
         ▼
 ┌─────────────────┐
 │  settings.py    │
 │  BaseURL laden  │
 └────────┬────────┘
          │
          ▼
 ┌───────────────────────────┐
 │ fetcher.py                │
 │ HTML mit requests holen   │
 └────────┬──────────────────┘
          │
          ▼
 ┌───────────────────────────────┐
 │ parser.py                     │
 │ HTML analysieren (Preis etc.) |
 └────────┬──────────────────────┘
          │
          ▼
 ┌─────────────────────────────┐
 │ sheets_writer.py            │
 │ Daten in Google Sheets      │
 └─────────────────────────────┘



