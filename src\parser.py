"""Parser module for extracting product data from HTML content.

This module contains functions to parse product pages from Metro website,
extracting product IDs, offers, prices, and vendor information.
"""

import json
import logging
import re
from typing import Dict, List, Any

from bs4 import BeautifulSoup


def extract_product_id_from_html(html: str) -> str:
    """Extract product ID directly from HTML content."""
    soup = BeautifulSoup(html, "lxml")
    script = soup.find("script", {"id": "__NUXT_DATA__"})

    if not script:
        raise ValueError("❌ Kein __NUXT_DATA__-Block gefunden.")

    try:
        # Parse the JSON data
        nuxt_data = json.loads(script.string)

        # Check each item in the array for product data
        for _, item in enumerate(nuxt_data):
            if isinstance(item, str) and "product" in item and "id" in item:
                try:
                    # Try to parse the item as JSON
                    item_data = json.loads(item)
                    if "product" in item_data and "id" in item_data["product"]:
                        return item_data["product"]["id"]
                except json.JSONDecodeError:
                    # If parsing fails, try regex
                    match = re.search(r'"product":\s*{\s*"id":\s*"([^"]+)"', item)
                    if match:
                        return match.group(1)

        raise ValueError("❌ Produkt-ID nicht im __NUXT_DATA__ gefunden.")

    except Exception as e:
        raise ValueError(f"❌ Fehler beim Parsen des __NUXT_DATA__: {e}") from e


def extract_offers_from_html(html: str) -> List[Dict[str, Any]]:
    """Extract offer data directly from HTML content."""
    soup = BeautifulSoup(html, "lxml")
    script = soup.find("script", {"id": "__NUXT_DATA__"})

    if not script:
        raise ValueError("❌ Kein __NUXT_DATA__-Block gefunden.")

    try:
        # Parse the JSON data
        nuxt_data = json.loads(script.string)

        # Check each item in the array for offer data
        for _, item in enumerate(nuxt_data):
            if isinstance(item, str) and "offers" in item:
                try:
                    # Try to parse the item as JSON
                    item_data = json.loads(item)
                    if "offers" in item_data and isinstance(item_data["offers"], list):
                        return item_data["offers"]
                except json.JSONDecodeError:
                    pass  # For now, we'll focus on JSON parsing

        raise ValueError("❌ Angebotsdaten nicht im __NUXT_DATA__ gefunden.")

    except Exception as e:
        raise ValueError(f"❌ Fehler beim Parsen des __NUXT_DATA__: {e}") from e


def parse_product_page(html: str, product_id: str) -> Dict[str, Any]:
    """Parse product data from HTML."""
    try:
        # Extract offers directly from the HTML
        try:
            offers = extract_offers_from_html(html)
        except Exception as e:  # pylint: disable=broad-exception-caught
            logging.warning("⚠️ Konnte Angebote nicht extrahieren: %s", e)
            offers = []

        # Initialize the result dictionary
        result = {
            "ID": product_id,
            "Price1": None,
            "Delivery1": None,
            "Price2": None,
            "Delivery2": None,
            "Vendor Name": None
        }

        if offers and len(offers) > 0:
            # Process the first offer
            first_offer = offers[0]

            # Extract price (remove € symbol and convert to float)
            price_str = first_offer.get('price', '0')
            if isinstance(price_str, str):
                price_str = price_str.replace('€', '').replace(',', '.').strip()
                result["Price1"] = float(price_str) if price_str else None

            # Extract delivery cost if available
            delivery_str = first_offer.get('shipping_cost', '0')
            if isinstance(delivery_str, str):
                delivery_str = delivery_str.replace('€', '').replace(',', '.').strip()
                result["Delivery1"] = float(delivery_str) if delivery_str else 0.0

            # Extract vendor name
            vendor = first_offer.get('supplier', {})
            if isinstance(vendor, dict):
                result["Vendor Name"] = vendor.get('name', 'Unbekannt').strip()

            # If there's a second offer, process it too
            if len(offers) > 1:
                second_offer = offers[1]

                # Extract price
                price_str = second_offer.get('price', '0')
                if isinstance(price_str, str):
                    price_str = price_str.replace('€', '').replace(',', '.').strip()
                    result["Price2"] = float(price_str) if price_str else None

                # Extract delivery cost
                delivery_str = second_offer.get('shipping_cost', '0')
                if isinstance(delivery_str, str):
                    delivery_str = delivery_str.replace('€', '').replace(',', '.').strip()
                    result["Delivery2"] = float(delivery_str) if delivery_str else 0.0

        return result

    except Exception as e:  # pylint: disable=broad-exception-caught
        logging.error("❌ Fehler beim Parsen der Produktseite: %s", e)
        return {
            "ID": product_id,
            "Price1": None,
            "Delivery1": None,
            "Price2": None,
            "Delivery2": None,
            "Vendor Name": None
        }
